package usecase

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"

	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/gateway/stripe"
	"github.com/smooth-inc/backend/internal/repository"
	"github.com/smooth-inc/backend/pkg/errors"
)

type paymentUsecase struct {
	paymentRepo       repository.PaymentRepository
	sessionRepo       repository.SessionRepository
	userRepo          repository.UserRepository
	paymentMethodRepo repository.PaymentMethodRepository
	disputeRepo       repository.DisputeRepository
	invoiceRepo       repository.InvoiceRepository
	stripeGateway     stripe.Gateway
}

func NewPaymentUsecase(
	paymentRepo repository.PaymentRepository,
	sessionRepo repository.SessionRepository,
	userRepo repository.UserRepository,
	paymentMethodRepo repository.PaymentMethodRepository,
	disputeRepo repository.DisputeRepository,
	invoiceRepo repository.InvoiceRepository,
	stripeGateway stripe.Gateway,
) PaymentUsecase {
	return &paymentUsecase{
		paymentRepo:       paymentRepo,
		sessionRepo:       sessionRepo,
		userRepo:          userRepo,
		paymentMethodRepo: paymentMethodRepo,
		disputeRepo:       disputeRepo,
		invoiceRepo:       invoiceRepo,
		stripeGateway:     stripeGateway,
	}
}

func (uc *paymentUsecase) CreatePaymentLink(ctx context.Context, sessionID uuid.UUID) (string, error) {
	session, err := uc.sessionRepo.GetByID(ctx, sessionID)
	if err != nil {
		return "", errors.NewNotFoundError("session not found")
	}

	if session.Status != domain.SessionStatusCompleted {
		return "", errors.NewBadRequestError("session must be completed to create payment link")
	}

	existingPayment, err := uc.paymentRepo.GetBySessionID(ctx, sessionID)
	if err == nil && existingPayment != nil {
		if existingPayment.StripePaymentLinkID != nil {
			return *existingPayment.StripePaymentLinkID, nil
		}
	}

	if session.UserID == nil {
		return "", errors.NewBadRequestError("session has no associated user")
	}

	amount := session.GetFinalAmount()
	if amount <= 0 {
		return "", errors.NewBadRequestError("invalid session fee amount")
	}

	payment, err := domain.NewPayment(sessionID, *session.UserID, amount)
	if err != nil {
		return "", errors.NewValidationError(err.Error())
	}

	paymentLink, err := uc.stripeGateway.CreatePaymentLink(ctx, amount, "JPY", fmt.Sprintf("Parking fee for session %s", sessionID.String()))
	if err != nil {
		return "", errors.NewExternalServiceError("failed to create Stripe payment link", err)
	}

	payment.SetStripePaymentLinkID(paymentLink.ID)
	if paymentLink.PaymentIntentID != nil {
		payment.SetStripePaymentIntentID(*paymentLink.PaymentIntentID)
	}

	if existingPayment != nil {
		payment.ID = existingPayment.ID
		if err := uc.paymentRepo.Update(ctx, payment); err != nil {
			return "", errors.NewDatabaseError("failed to update payment", err)
		}
	} else {
		if err := uc.paymentRepo.Create(ctx, payment); err != nil {
			return "", errors.NewDatabaseError("failed to create payment", err)
		}
	}

	return paymentLink.URL, nil
}

func (uc *paymentUsecase) ProcessWebhook(ctx context.Context, payload []byte, signature string) error {
	event, err := uc.stripeGateway.VerifyWebhook(ctx, payload, signature)
	if err != nil {
		return errors.NewBadRequestError("invalid webhook signature")
	}

	switch event.Type {
	case "charge.dispute.created":
		return uc.handleChargeDisputeCreated(ctx, event.Data)
	case "charge.failed":
		return uc.handleChargeFailed(ctx, event.Data)
	case "charge.succeeded":
		return uc.handleChargeSucceeded(ctx, event.Data)
	case "customer.created":
		return uc.handleCustomerCreated(ctx, event.Data)
	case "customer.deleted":
		return uc.handleCustomerDeleted(ctx, event.Data)
	case "customer.updated":
		return uc.handleCustomerUpdated(ctx, event.Data)
	case "invoice.payment_failed":
		return uc.handleInvoicePaymentFailed(ctx, event.Data)
	case "invoice.payment_succeeded":
		return uc.handleInvoicePaymentSucceeded(ctx, event.Data)
	case "payment_intent.canceled":
		return uc.handlePaymentIntentCanceled(ctx, event.Data)
	case "payment_intent.payment_failed":
		return uc.handlePaymentIntentFailed(ctx, event.Data)
	case "payment_intent.requires_action":
		return uc.handlePaymentIntentRequiresAction(ctx, event.Data)
	case "payment_intent.succeeded":
		return uc.handlePaymentIntentSucceeded(ctx, event.Data)
	case "payment_method.attached":
		return uc.handlePaymentMethodAttached(ctx, event.Data)
	case "payment_method.detached":
		return uc.handlePaymentMethodDetached(ctx, event.Data)
	case "payment_method.updated":
		return uc.handlePaymentMethodUpdated(ctx, event.Data)
	case "setup_intent.canceled":
		return uc.handleSetupIntentCanceled(ctx, event.Data)
	case "setup_intent.requires_action":
		return uc.handleSetupIntentRequiresAction(ctx, event.Data)
	case "setup_intent.setup_failed":
		return uc.handleSetupIntentSetupFailed(ctx, event.Data)
	case "setup_intent.succeeded":
		return uc.handleSetupIntentSucceeded(ctx, event.Data)
	default:
		return nil
	}
}

func (uc *paymentUsecase) handlePaymentIntentSucceeded(ctx context.Context, data map[string]interface{}) error {
	paymentIntentID, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid payment intent ID")
	}

	payment, err := uc.paymentRepo.GetByStripePaymentIntentID(ctx, paymentIntentID)
	if err != nil {
		return errors.NewNotFoundError("payment not found")
	}

	payment.MarkAsCompleted()

	if receiptURL, ok := data["receipt_url"].(string); ok {
		payment.SetReceiptURL(receiptURL)
	}

	return uc.paymentRepo.Update(ctx, payment)
}

func (uc *paymentUsecase) handlePaymentIntentFailed(ctx context.Context, data map[string]interface{}) error {
	paymentIntentID, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid payment intent ID")
	}

	payment, err := uc.paymentRepo.GetByStripePaymentIntentID(ctx, paymentIntentID)
	if err != nil {
		return errors.NewNotFoundError("payment not found")
	}

	failureReason := "Payment failed"
	if lastPaymentError, ok := data["last_payment_error"].(map[string]interface{}); ok {
		if message, ok := lastPaymentError["message"].(string); ok {
			failureReason = message
		}
	}

	payment.MarkAsFailed(failureReason)
	return uc.paymentRepo.Update(ctx, payment)
}

func (uc *paymentUsecase) handleChargeDisputeCreated(ctx context.Context, data map[string]interface{}) error {
	stripeDisputeID, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid dispute ID")
	}

	stripeChargeID, ok := data["charge"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid charge ID")
	}

	amount, ok := data["amount"].(float64)
	if !ok {
		return errors.NewBadRequestError("invalid dispute amount")
	}

	currency, ok := data["currency"].(string)
	if !ok {
		currency = "JPY"
	}

	reason, ok := data["reason"].(string)
	if !ok {
		reason = "general"
	}

	status, ok := data["status"].(string)
	if !ok {
		status = "needs_response"
	}

	dispute, err := domain.NewDispute(stripeDisputeID, stripeChargeID, int(amount), currency, domain.DisputeReason(reason), domain.DisputeStatus(status))
	if err != nil {
		return errors.NewValidationError(err.Error())
	}

	if evidenceDueBy, ok := data["evidence_due_by"].(float64); ok {
		evidenceTime := time.Unix(int64(evidenceDueBy), 0)
		dispute.SetEvidenceDueBy(evidenceTime)
	}

	if isChargeRefundable, ok := data["is_charge_refundable"].(bool); ok {
		dispute.IsChargeRefundable = isChargeRefundable
	}

	if liveMode, ok := data["livemode"].(bool); ok {
		dispute.LiveMode = liveMode
	}

	if metadata, ok := data["metadata"].(map[string]interface{}); ok {
		metadataMap := make(map[string]string)
		for k, v := range metadata {
			if str, ok := v.(string); ok {
				metadataMap[k] = str
			}
		}
		dispute.Metadata = metadataMap
	}

	if networkReasonCode, ok := data["network_reason_code"].(string); ok {
		dispute.NetworkReasonCode = &networkReasonCode
	}

	if paymentIntentID, ok := data["payment_intent"].(string); ok {
		payment, err := uc.paymentRepo.GetByStripePaymentIntentID(ctx, paymentIntentID)
		if err == nil && payment != nil {
			dispute.SetPaymentID(payment.ID)
		}
	}

	return uc.disputeRepo.Create(ctx, dispute)
}

func (uc *paymentUsecase) handleChargeFailed(ctx context.Context, data map[string]interface{}) error {
	paymentIntentID, ok := data["payment_intent"].(string)
	if !ok {
		return nil
	}

	payment, err := uc.paymentRepo.GetByStripePaymentIntentID(ctx, paymentIntentID)
	if err != nil {
		return nil
	}

	failureReason := "Charge failed"
	if failureCode, ok := data["failure_code"].(string); ok {
		failureReason = failureCode
	}
	if failureMessage, ok := data["failure_message"].(string); ok {
		failureReason = failureMessage
	}

	payment.MarkAsFailed(failureReason)
	return uc.paymentRepo.Update(ctx, payment)
}

func (uc *paymentUsecase) handleChargeSucceeded(ctx context.Context, data map[string]interface{}) error {
	paymentIntentID, ok := data["payment_intent"].(string)
	if !ok {
		return nil
	}

	payment, err := uc.paymentRepo.GetByStripePaymentIntentID(ctx, paymentIntentID)
	if err != nil {
		return nil
	}

	payment.MarkAsCompleted()

	if receiptURL, ok := data["receipt_url"].(string); ok {
		payment.SetReceiptURL(receiptURL)
	}

	if paymentMethodDetails, ok := data["payment_method_details"].(map[string]interface{}); ok {
		if card, ok := paymentMethodDetails["card"].(map[string]interface{}); ok {
			if brand, ok := card["brand"].(string); ok {
				if last4, ok := card["last4"].(string); ok {
					payment.SetPaymentMethod("card", last4, brand)
				}
			}
		}
	}

	return uc.paymentRepo.Update(ctx, payment)
}

func (uc *paymentUsecase) handleCustomerCreated(ctx context.Context, data map[string]interface{}) error {
	stripeCustomerID, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid customer ID")
	}

	email, ok := data["email"].(string)
	if !ok {
		return nil
	}

	user, err := uc.userRepo.GetByEmail(ctx, email)
	if err != nil {
		return nil
	}

	if user.StripeCustomerID == nil || *user.StripeCustomerID != stripeCustomerID {
		user.SetStripeCustomerID(stripeCustomerID)
		return uc.userRepo.Update(ctx, user)
	}

	return nil
}

func (uc *paymentUsecase) handleCustomerDeleted(ctx context.Context, data map[string]interface{}) error {
	stripeCustomerID, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid customer ID")
	}

	user, err := uc.userRepo.GetByStripeCustomerID(ctx, stripeCustomerID)
	if err != nil {
		return nil
	}

	user.StripeCustomerID = nil
	return uc.userRepo.Update(ctx, user)
}

func (uc *paymentUsecase) handleCustomerUpdated(ctx context.Context, data map[string]interface{}) error {
	stripeCustomerID, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid customer ID")
	}

	user, err := uc.userRepo.GetByStripeCustomerID(ctx, stripeCustomerID)
	if err != nil {
		return nil
	}

	updated := false

	if email, ok := data["email"].(string); ok && email != "" {
		if user.Email != strings.ToLower(email) {
			if err := user.UpdateEmail(email); err != nil {
				return errors.NewValidationError(err.Error())
			}
			updated = true
		}
	}

	if name, ok := data["name"].(string); ok && name != "" {
		if user.Name != name {
			user.Name = name
			updated = true
		}
	}

	if updated {
		user.UpdatedAt = time.Now()
		return uc.userRepo.Update(ctx, user)
	}

	return nil
}

func (uc *paymentUsecase) handleInvoicePaymentFailed(ctx context.Context, data map[string]interface{}) error {
	stripeInvoiceID, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid invoice ID")
	}

	invoice, err := uc.invoiceRepo.GetByStripeInvoiceID(ctx, stripeInvoiceID)
	if err != nil {
		stripeCustomerID, ok := data["customer"].(string)
		if !ok {
			return nil
		}

		amount, ok := data["amount_due"].(float64)
		if !ok {
			amount = 0
		}

		currency, ok := data["currency"].(string)
		if !ok {
			currency = "JPY"
		}

		invoice, err = domain.NewInvoice(stripeInvoiceID, stripeCustomerID, int(amount), currency, domain.InvoiceStatusOpen)
		if err != nil {
			return errors.NewValidationError(err.Error())
		}

		user, err := uc.userRepo.GetByStripeCustomerID(ctx, stripeCustomerID)
		if err == nil {
			invoice.SetUserID(user.ID)
		}

		if description, ok := data["description"].(string); ok {
			invoice.Description = &description
		}

		if hostedInvoiceURL, ok := data["hosted_invoice_url"].(string); ok {
			invoice.HostedInvoiceURL = &hostedInvoiceURL
		}

		if err := uc.invoiceRepo.Create(ctx, invoice); err != nil {
			return errors.NewDatabaseError("failed to create invoice", err)
		}
	}

	invoice.MarkAsFailed()
	if attemptCount, ok := data["attempt_count"].(float64); ok {
		invoice.AttemptCount = int(attemptCount)
	}
	invoice.Attempted = true

	return uc.invoiceRepo.Update(ctx, invoice)
}

func (uc *paymentUsecase) handleInvoicePaymentSucceeded(ctx context.Context, data map[string]interface{}) error {
	stripeInvoiceID, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid invoice ID")
	}

	invoice, err := uc.invoiceRepo.GetByStripeInvoiceID(ctx, stripeInvoiceID)
	if err != nil {
		stripeCustomerID, ok := data["customer"].(string)
		if !ok {
			return nil
		}

		amount, ok := data["amount_paid"].(float64)
		if !ok {
			amount = 0
		}

		currency, ok := data["currency"].(string)
		if !ok {
			currency = "JPY"
		}

		invoice, err = domain.NewInvoice(stripeInvoiceID, stripeCustomerID, int(amount), currency, domain.InvoiceStatusPaid)
		if err != nil {
			return errors.NewValidationError(err.Error())
		}

		user, err := uc.userRepo.GetByStripeCustomerID(ctx, stripeCustomerID)
		if err == nil {
			invoice.SetUserID(user.ID)
		}

		if description, ok := data["description"].(string); ok {
			invoice.Description = &description
		}

		if hostedInvoiceURL, ok := data["hosted_invoice_url"].(string); ok {
			invoice.HostedInvoiceURL = &hostedInvoiceURL
		}

		if invoicePDF, ok := data["invoice_pdf"].(string); ok {
			invoice.InvoicePDF = &invoicePDF
		}

		if err := uc.invoiceRepo.Create(ctx, invoice); err != nil {
			return errors.NewDatabaseError("failed to create invoice", err)
		}
	}

	if statusUpdateTime, ok := data["status_transitions"].(map[string]interface{}); ok {
		if paidAt, ok := statusUpdateTime["paid_at"].(float64); ok {
			paidTime := time.Unix(int64(paidAt), 0)
			invoice.MarkAsPaid(paidTime)
		}
	}

	if paymentIntentID, ok := data["payment_intent"].(string); ok {
		invoice.SetPaymentIntentID(paymentIntentID)
	}

	if attemptCount, ok := data["attempt_count"].(float64); ok {
		invoice.AttemptCount = int(attemptCount)
	}
	invoice.Attempted = true

	return uc.invoiceRepo.Update(ctx, invoice)
}

func (uc *paymentUsecase) handlePaymentIntentCanceled(ctx context.Context, data map[string]interface{}) error {
	paymentIntentID, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid payment intent ID")
	}

	payment, err := uc.paymentRepo.GetByStripePaymentIntentID(ctx, paymentIntentID)
	if err != nil {
		return nil
	}

	payment.MarkAsFailed("Payment intent canceled")
	return uc.paymentRepo.Update(ctx, payment)
}

func (uc *paymentUsecase) handlePaymentIntentRequiresAction(ctx context.Context, data map[string]interface{}) error {
	paymentIntentID, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid payment intent ID")
	}

	payment, err := uc.paymentRepo.GetByStripePaymentIntentID(ctx, paymentIntentID)
	if err != nil {
		return nil
	}

	payment.SetStripeStatus("requires_action")
	return uc.paymentRepo.Update(ctx, payment)
}

func (uc *paymentUsecase) handlePaymentMethodAttached(ctx context.Context, data map[string]interface{}) error {
	stripePaymentMethodID, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid payment method ID")
	}

	customerID, ok := data["customer"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid customer ID")
	}

	user, err := uc.userRepo.GetByStripeCustomerID(ctx, customerID)
	if err != nil {
		return errors.NewNotFoundError("user not found for customer ID")
	}

	existingPM, err := uc.paymentMethodRepo.GetByStripePaymentMethodID(ctx, stripePaymentMethodID)
	if err == nil && existingPM != nil {
		if existingPM.UserID != user.ID {
			existingPM.UserID = user.ID
			existingPM.UpdatedAt = time.Now()
			return uc.paymentMethodRepo.Update(ctx, existingPM)
		}
		return nil
	}

	stripePaymentMethod, err := uc.stripeGateway.GetPaymentMethod(ctx, stripePaymentMethodID)
	if err != nil {
		return errors.NewExternalServiceError("failed to retrieve payment method from Stripe", err)
	}

	paymentMethodType := domain.PaymentMethodTypeCard
	if stripePaymentMethod.Type == "paypay" {
		paymentMethodType = domain.PaymentMethodTypePayPay
	}

	paymentMethod, err := domain.NewPaymentMethod(user.ID, stripePaymentMethodID, paymentMethodType)
	if err != nil {
		return errors.NewValidationError(err.Error())
	}

	if stripePaymentMethod.Type == "card" && stripePaymentMethod.Brand != nil {
		paymentMethod.SetCardDetails(
			*stripePaymentMethod.Brand,
			*stripePaymentMethod.Last4,
			*stripePaymentMethod.ExpiryMonth,
			*stripePaymentMethod.ExpiryYear,
		)
	}

	userPaymentMethods, err := uc.paymentMethodRepo.GetByUserID(ctx, user.ID)
	if err != nil {
		return errors.NewDatabaseError("failed to get user payment methods", err)
	}

	if len(userPaymentMethods) == 0 {
		paymentMethod.SetAsDefault()
		paymentMethodIDStr := paymentMethod.ID.String()
		user.DefaultPaymentMethodID = &paymentMethodIDStr
		if err := uc.userRepo.Update(ctx, user); err != nil {
			return errors.NewDatabaseError("failed to update user default payment method", err)
		}
	}

	if err := uc.paymentMethodRepo.Create(ctx, paymentMethod); err != nil {
		return errors.NewDatabaseError("failed to create payment method", err)
	}

	if paymentMethod.IsDefault && user.DefaultPaymentMethodID == nil {
		paymentMethodIDStr := paymentMethod.ID.String()
		user.DefaultPaymentMethodID = &paymentMethodIDStr
		return uc.userRepo.Update(ctx, user)
	}

	return nil
}

func (uc *paymentUsecase) handlePaymentMethodDetached(ctx context.Context, data map[string]interface{}) error {
	stripePaymentMethodID, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid payment method ID")
	}

	paymentMethod, err := uc.paymentMethodRepo.GetByStripePaymentMethodID(ctx, stripePaymentMethodID)
	if err != nil {
		return nil
	}

	wasDefault := paymentMethod.IsDefault
	userID := paymentMethod.UserID

	if err := uc.paymentMethodRepo.Delete(ctx, paymentMethod.ID); err != nil {
		return errors.NewDatabaseError("failed to delete payment method", err)
	}

	if wasDefault {
		user, err := uc.userRepo.GetByID(ctx, userID)
		if err != nil {
			return errors.NewDatabaseError("failed to get user for default payment method cleanup", err)
		}

		user.DefaultPaymentMethodID = nil
		if err := uc.userRepo.Update(ctx, user); err != nil {
			return errors.NewDatabaseError("failed to clear user default payment method", err)
		}

		remainingMethods, err := uc.paymentMethodRepo.GetByUserID(ctx, userID)
		if err == nil && len(remainingMethods) > 0 {
			newDefault := remainingMethods[0]
			newDefault.SetAsDefault()
			if err := uc.paymentMethodRepo.Update(ctx, newDefault); err == nil {
				newDefaultIDStr := newDefault.ID.String()
				user.DefaultPaymentMethodID = &newDefaultIDStr
				if err := uc.userRepo.Update(ctx, user); err != nil {
					return errors.NewDatabaseError("failed to set new default payment method", err)
				}
			}
		}
	}

	return nil
}

func (uc *paymentUsecase) handlePaymentMethodUpdated(ctx context.Context, data map[string]interface{}) error {
	stripePaymentMethodID, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid payment method ID")
	}

	paymentMethod, err := uc.paymentMethodRepo.GetByStripePaymentMethodID(ctx, stripePaymentMethodID)
	if err != nil {
		return nil
	}

	stripePaymentMethod, err := uc.stripeGateway.GetPaymentMethod(ctx, stripePaymentMethodID)
	if err != nil {
		return errors.NewExternalServiceError("failed to retrieve updated payment method from Stripe", err)
	}

	if customerID, ok := data["customer"].(string); ok && customerID != "" {
		user, err := uc.userRepo.GetByStripeCustomerID(ctx, customerID)
		if err == nil && user.ID != paymentMethod.UserID {
			paymentMethod.UserID = user.ID
		}
	}

	if stripePaymentMethod.Type == "card" && stripePaymentMethod.Brand != nil {
		paymentMethod.SetCardDetails(
			*stripePaymentMethod.Brand,
			*stripePaymentMethod.Last4,
			*stripePaymentMethod.ExpiryMonth,
			*stripePaymentMethod.ExpiryYear,
		)
	}

	return uc.paymentMethodRepo.Update(ctx, paymentMethod)
}

func (uc *paymentUsecase) handleSetupIntentCanceled(ctx context.Context, data map[string]interface{}) error {
	_, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid setup intent ID")
	}

	customerID, ok := data["customer"].(string)
	if !ok {
		return nil
	}

	_, err := uc.userRepo.GetByStripeCustomerID(ctx, customerID)
	if err != nil {
		return nil
	}

	return nil
}

func (uc *paymentUsecase) handleSetupIntentRequiresAction(ctx context.Context, data map[string]interface{}) error {
	_, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid setup intent ID")
	}

	customerID, ok := data["customer"].(string)
	if !ok {
		return nil
	}

	_, err := uc.userRepo.GetByStripeCustomerID(ctx, customerID)
	if err != nil {
		return nil
	}

	return nil
}

func (uc *paymentUsecase) handleSetupIntentSetupFailed(ctx context.Context, data map[string]interface{}) error {
	_, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid setup intent ID")
	}

	customerID, ok := data["customer"].(string)
	if !ok {
		return nil
	}

	_, err := uc.userRepo.GetByStripeCustomerID(ctx, customerID)
	if err != nil {
		return nil
	}

	return nil
}

func (uc *paymentUsecase) handleSetupIntentSucceeded(ctx context.Context, data map[string]interface{}) error {
	customerID, ok := data["customer"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid customer ID")
	}

	paymentMethodID, ok := data["payment_method"].(string)
	if !ok {
		return nil
	}

	user, err := uc.userRepo.GetByStripeCustomerID(ctx, customerID)
	if err != nil {
		return errors.NewNotFoundError("user not found for customer ID")
	}

	existingPM, err := uc.paymentMethodRepo.GetByStripePaymentMethodID(ctx, paymentMethodID)
	if err == nil && existingPM != nil {
		if existingPM.UserID != user.ID {
			existingPM.UserID = user.ID
			existingPM.UpdatedAt = time.Now()
			return uc.paymentMethodRepo.Update(ctx, existingPM)
		}
		return nil
	}

	stripePaymentMethod, err := uc.stripeGateway.GetPaymentMethod(ctx, paymentMethodID)
	if err != nil {
		return errors.NewExternalServiceError("failed to retrieve payment method from Stripe", err)
	}

	paymentMethodType := domain.PaymentMethodTypeCard
	if stripePaymentMethod.Type == "paypay" {
		paymentMethodType = domain.PaymentMethodTypePayPay
	}

	paymentMethod, err := domain.NewPaymentMethod(user.ID, paymentMethodID, paymentMethodType)
	if err != nil {
		return errors.NewValidationError(err.Error())
	}

	if stripePaymentMethod.Type == "card" && stripePaymentMethod.Brand != nil {
		paymentMethod.SetCardDetails(
			*stripePaymentMethod.Brand,
			*stripePaymentMethod.Last4,
			*stripePaymentMethod.ExpiryMonth,
			*stripePaymentMethod.ExpiryYear,
		)
	}

	userPaymentMethods, err := uc.paymentMethodRepo.GetByUserID(ctx, user.ID)
	if err != nil {
		return errors.NewDatabaseError("failed to get user payment methods", err)
	}

	if len(userPaymentMethods) == 0 {
		paymentMethod.SetAsDefault()
		paymentMethodIDStr := paymentMethod.ID.String()
		user.DefaultPaymentMethodID = &paymentMethodIDStr
		if err := uc.userRepo.Update(ctx, user); err != nil {
			return errors.NewDatabaseError("failed to update user default payment method", err)
		}
	}

	if err := uc.paymentMethodRepo.Create(ctx, paymentMethod); err != nil {
		return errors.NewDatabaseError("failed to create payment method", err)
	}

	if paymentMethod.IsDefault && user.DefaultPaymentMethodID == nil {
		paymentMethodIDStr := paymentMethod.ID.String()
		user.DefaultPaymentMethodID = &paymentMethodIDStr
		return uc.userRepo.Update(ctx, user)
	}

	return nil
}

func (uc *paymentUsecase) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Payment, error) {
	return uc.paymentRepo.GetByUserID(ctx, userID, limit, offset)
}

func (uc *paymentUsecase) ProcessAutoPayment(ctx context.Context, sessionID uuid.UUID) (*domain.Payment, error) {
	session, err := uc.sessionRepo.GetByID(ctx, sessionID)
	if err != nil {
		return nil, errors.NewNotFoundError("session not found")
	}

	if session.Status != domain.SessionStatusCompleted {
		return nil, errors.NewBadRequestError("session must be completed to process auto payment")
	}

	if session.UserID == nil {
		return nil, errors.NewBadRequestError("session has no associated user")
	}

	user, err := uc.userRepo.GetByID(ctx, *session.UserID)
	if err != nil {
		return nil, errors.NewNotFoundError("user not found")
	}

	if !user.AutoPaymentEnabled {
		return nil, errors.NewBadRequestError("auto payment is not enabled for this user")
	}

	if user.DefaultPaymentMethodID == nil {
		return nil, errors.NewBadRequestError("user has no default payment method")
	}

	defaultPaymentMethodID, err := uuid.Parse(*user.DefaultPaymentMethodID)
	if err != nil {
		return nil, errors.NewBadRequestError("invalid default payment method ID")
	}

	paymentMethod, err := uc.paymentMethodRepo.GetByID(ctx, defaultPaymentMethodID)
	if err != nil {
		return nil, errors.NewNotFoundError("default payment method not found")
	}

	if !paymentMethod.CanBeUsedForPayment() {
		return nil, errors.NewBadRequestError("default payment method cannot be used for payment")
	}

	amount := session.GetFinalAmount()
	if amount <= 0 {
		return nil, errors.NewBadRequestError("invalid session fee amount")
	}

	existingPayment, err := uc.paymentRepo.GetBySessionID(ctx, sessionID)
	if err == nil && existingPayment != nil && existingPayment.IsCompleted() {
		return existingPayment, nil
	}

	payment, err := domain.NewPayment(sessionID, *session.UserID, amount)
	if err != nil {
		return nil, errors.NewValidationError(err.Error())
	}

	if user.StripeCustomerID == nil {
		return nil, errors.NewBadRequestError("user has no Stripe customer ID")
	}

	paymentIntent, err := uc.stripeGateway.CreatePaymentIntent(ctx, amount, "JPY", *user.StripeCustomerID, paymentMethod.StripePaymentMethodID)
	if err != nil {
		return nil, errors.NewExternalServiceError("failed to create payment intent", err)
	}

	payment.SetStripePaymentIntentID(paymentIntent.ID)
	payment.MarkAsProcessing()

	if paymentMethod.Brand != nil && paymentMethod.Last4 != nil {
		payment.SetPaymentMethod(string(paymentMethod.Type), *paymentMethod.Last4, *paymentMethod.Brand)
	}

	if existingPayment != nil {
		payment.ID = existingPayment.ID
		if err := uc.paymentRepo.Update(ctx, payment); err != nil {
			return nil, errors.NewDatabaseError("failed to update payment", err)
		}
	} else {
		if err := uc.paymentRepo.Create(ctx, payment); err != nil {
			return nil, errors.NewDatabaseError("failed to create payment", err)
		}
	}

	confirmResult, err := uc.stripeGateway.ConfirmPaymentIntent(ctx, paymentIntent.ID)
	if err != nil {
		payment.MarkAsFailed("Failed to confirm payment")
		uc.paymentRepo.Update(ctx, payment)
		return nil, errors.NewExternalServiceError("failed to confirm payment intent", err)
	}

	if confirmResult.Status == "succeeded" {
		payment.MarkAsCompleted()
		if confirmResult.ReceiptURL != "" {
			payment.SetReceiptURL(confirmResult.ReceiptURL)
		}
	} else if confirmResult.Status == "requires_action" {
		return nil, errors.NewBadRequestError("payment requires additional authentication")
	} else {
		payment.MarkAsFailed("Payment confirmation failed")
	}

	if err := uc.paymentRepo.Update(ctx, payment); err != nil {
		return nil, errors.NewDatabaseError("failed to update payment status", err)
	}

	return payment, nil
}
